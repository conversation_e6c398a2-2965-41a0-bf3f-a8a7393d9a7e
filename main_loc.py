from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
import xml.etree.ElementTree as ET
import time
from wechatpy.crypto import WXBizMsgCrypt
from typing import Dict, Any

# 配置信息（需从企业微信管理端获取）
TOKEN = "your_token"  # 机器人配置的Token
ENCODING_AES_KEY = "your_encoding_aes_key"  # EncodingAESKey
CORP_ID = "your_corp_id"  # 企业ID

app = FastAPI(title="企业微信智能机器人后台")

# 允许跨域（调试时使用，生产环境可按需配置）
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/wechat/robot/callback")
async def verify_url(
    msg_signature: str,
    timestamp: str,
    nonce: str,
    echostr: str
):
    """验证接收URL的有效性（企业微信首次请求时调用）"""
    try:
        wxcpt = WXBizMsgCrypt(TOKEN, ENCODING_AES_KEY, CORP_ID)
        ret, s_echo_str = wxcpt.verify_url(msg_signature, timestamp, nonce, echostr)
        if ret != 0:
            return Response(content="验证失败", status_code=400)
        return Response(content=s_echo_str, media_type="text/plain")
    except Exception as e:
        return Response(content=f"验证异常: {str(e)}", status_code=500)


@app.post("/wechat/robot/callback")
async def handle_robot_message(request: Request):
    """处理企业微信机器人回调的消息"""
    try:
        # 获取请求参数
        query_params = request.query_params
        msg_signature = query_params.get("msg_signature")
        timestamp = query_params.get("timestamp")
        nonce = query_params.get("nonce")
        
        # 读取请求体（XML加密数据）
        body = await request.body()
        xml_data = body.decode("utf-8")
        encrypt = ET.fromstring(xml_data).find("Encrypt").text
        
        # 解密消息
        wxcpt = WXBizMsgCrypt(TOKEN, ENCODING_AES_KEY, CORP_ID)
        ret, decrypted_xml = wxcpt.decrypt_message(encrypt, msg_signature, timestamp, nonce)
        if ret != 0:
            return Response(content="消息解密失败", status_code=400)
        
        # 解析解密后的XML
        msg_root = ET.fromstring(decrypted_xml)
        msg_type = msg_root.find("MsgType").text
        from_user = msg_root.find("FromUserName").text
        to_user = msg_root.find("ToUserName").text
        
        # 处理不同类型的消息
        reply_content = handle_message_type(msg_type, msg_root)
        
        # 构造回复XML
        reply_xml = generate_reply_xml(
            to_user=from_user,
            from_user=to_user,
            msg_type="text",
            content=reply_content
        )
        
        # 加密回复消息
        ret, encrypted_reply = wxcpt.encrypt_message(reply_xml, nonce, timestamp)
        if ret != 0:
            return Response(content="回复加密失败", status_code=500)
        
        return Response(content=encrypted_reply, media_type="text/plain")
    
    except Exception as e:
        # 记录异常日志（实际项目中建议使用专业日志库）
        print(f"处理消息异常: {str(e)}")
        return Response(content="服务器内部错误", status_code=500)


def handle_message_type(msg_type: str, msg_root: ET.Element) -> str:
    """处理不同类型的消息，返回回复内容"""
    if msg_type == "text":
        content = msg_root.find("Content").text
        return f"收到文本消息: {content}"
    
    elif msg_type == "image":
        media_id = msg_root.find("MediaId").text
        pic_url = msg_root.find("PicUrl").text
        return f"收到图片，MediaID: {media_id}, 图片地址: {pic_url}"
    
    elif msg_type == "news":
        articles = []
        for article in msg_root.findall("Articles/Article"):
            title = article.find("Title").text
            url = article.find("Url").text
            articles.append(f"标题: {title}, 链接: {url}")
        return f"收到图文消息，共{len(articles)}篇\n" + "\n".join(articles)
    
    else:
        return "暂不支持的消息类型"


def generate_reply_xml(to_user: str, from_user: str, msg_type: str, content: str) -> str:
    """生成回复消息的XML"""
    create_time = int(time.time())
    return f"""
    <xml>
        <ToUserName><![CDATA[{to_user}]]></ToUserName>
        <FromUserName><![CDATA[{from_user}]]></FromUserName>
        <CreateTime>{create_time}</CreateTime>
        <MsgType><![CDATA[{msg_type}]]></MsgType>
        <Content><![CDATA[{content}]]></Content>
    </xml>
    """.strip()